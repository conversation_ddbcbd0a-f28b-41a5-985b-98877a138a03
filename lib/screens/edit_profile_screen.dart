import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:healo/common/utils/size.dart';
import 'package:healo/common/utils/snackbar.dart';
import 'package:healo/common/utils/validators.dart';
import 'package:healo/common/widgets/custom_app_bar.dart';
import 'package:healo/common/widgets/custom_button.dart';
import 'package:healo/common/widgets/custom_textformfield.dart';

import 'package:healo/constants/app_colors.dart';
import 'package:healo/models/users_model.dart';
import 'package:healo/providers/user_provider.dart';
import 'package:healo/route/route_constants.dart';
import 'package:healo/services/auth_service.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:intl/intl.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  ConsumerState<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _dobController = TextEditingController();
  final TextEditingController _genderController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();

  final GlobalKey _genderFieldKey = GlobalKey();
  final LayerLink _layerLink = LayerLink();
  OverlayEntry? _overlayEntry;
  bool _isDropdownOpen = false;

  bool _isLoading = false;
  Users? _currentUser;

  final AuthService authService = AuthService();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    // First dispose controllers
    _nameController.dispose();
    _dobController.dispose();
    _genderController.dispose();
    _phoneController.dispose();

    // Safely remove overlay if it exists
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
    }

    super.dispose();
  }

  void _toggleDropdown() {
    if (_isDropdownOpen) {
      _removeDropdown();
    } else {
      _showDropdown();
    }
  }

  void _showDropdown() {
    _overlayEntry = _createDropdown();
    Overlay.of(context).insert(_overlayEntry!);
    setState(() => _isDropdownOpen = true);
  }

  void _removeDropdown() {
    if (_overlayEntry != null) {
      _overlayEntry!.remove();
      _overlayEntry = null;
      if (mounted) {
        setState(() => _isDropdownOpen = false);
      }
    }
  }

  OverlayEntry _createDropdown() {
    return OverlayEntry(
      builder: (context) => Positioned(
        width: SizeConfig.screenWidth! - 30, // Match text field width
        child: CompositedTransformFollower(
          link: _layerLink,
          offset: Offset(0, 50), // Position dropdown below the field
          child: Material(
            elevation: MySize.size4,
            borderRadius: Shape.circular(MySize.size10),
            color: Theme.of(context).cardColor,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: _genderOptions.map((String gender) {
                return ListTile(
                  title: Text(
                    gender,
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                  onTap: () {
                    setState(() {
                      _genderController.text = gender;
                    });
                    _removeDropdown();
                  },
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load user data
      _currentUser = await FirestoreService().getUser();

      if (_currentUser != null) {
        _nameController.text = _currentUser!.name ?? '';
        _dobController.text = _currentUser!.dob ?? '';
        _genderController.text = _currentUser!.gender ?? '';
      }

      // Load phone number
      final phone = ref.read(userPhoneProvider);
      _phoneController.text = phone ?? '';
    } catch (e) {
      if (mounted) {
        customSnackBar(context, "Error loading user data: ${e.toString()}",
            color: Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime initialDate;

    try {
      // Try to parse the date with format dd-MM-yyyy
      if (_dobController.text.isNotEmpty && _dobController.text.contains('-')) {
        initialDate = DateFormat('dd-MM-yyyy').parse(_dobController.text);
      } else {
        initialDate = DateTime.now();
      }
    } catch (e) {
      // If parsing fails, use current date
      initialDate = DateTime.now();
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.dark(
              primary: AppColors.primaryColor.withAlpha(200),
              // Header and selected day color
              onPrimary: Colors.black,
              // Header text and selected day text color
              surface: AppColors.white,
              // Calendar background color
              onSurface: AppColors.black, // Calendar text color
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        // Use the same format as stored in the database (dd-MM-yyyy)
        _dobController.text = DateFormat('dd-MM-yyyy').format(picked);
      });
    }
  }

  // Gender options
  final List<String> _genderOptions = ["Male", "Female", "Other"];

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create updated user object
      Users updatedUser = Users(
        name: _nameController.text,
        dob: _dobController.text,
        gender: _genderController.text,
        // Preserve existing values
        weight: _currentUser?.weight,
        height: _currentUser?.height,
        initialDataCompleted: _currentUser?.initialDataCompleted ?? true,
        // Don't set fcmToken - let it remain null so it's excluded from update
      );

      // Update user in Firestore using safe method
      await FirestoreService().updateUserSafely(updatedUser);

      // Refresh user data in providers
      ref.invalidate(userDataProvider);

      if (mounted) {
        customSnackBar(context, "Profile updated successfully",
            color: AppColors.primaryColor);
      }
    } catch (e) {
      if (mounted) {
        customSnackBar(context, "Error updating profile: ${e.toString()}",
            color: Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_isDropdownOpen,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (!didPop && _isDropdownOpen) {
          _removeDropdown();
        }
      },
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: const CustomAppBar(
          title: "Edit Profile",
          showBackButton: true,
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(MySize.size15),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Space.height(20),
                        CustomTextformfield(
                          label: "Your Name",
                          controller: _nameController,
                          validator: (value) =>
                              Validators.validator(value, "Name"),
                        ),
                        Space.height(20),
                        CompositedTransformTarget(
                          link: _layerLink,
                          child: CustomTextformfield(
                            key: _genderFieldKey,
                            label: "Gender",
                            controller: _genderController,
                            readOnly: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Please select your gender";
                              }
                              return null;
                            },
                            suffixIcon: InkWell(
                              onTap: _toggleDropdown,
                              child: Icon(
                                _isDropdownOpen
                                    ? Icons.arrow_drop_up
                                    : Icons.arrow_drop_down,
                                size: MySize.size24,
                                color: AppColors.black,
                              ),
                            ),
                          ),
                        ),
                        Space.height(20),
                        CustomTextformfield(
                          label: "Date Of Birth",
                          controller: _dobController,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return "Please select your date of birth";
                            }
                            return null;
                          },
                          readOnly: true,
                          suffixIcon: InkWell(
                            onTap: () => _selectDate(context),
                            child: Icon(
                              Icons.calendar_month,
                              size: MySize.size22,
                            ),
                          ),
                        ),
                        Space.height(20),
                        CustomTextformfield(
                          label: "Phone Number",
                          controller: _phoneController,
                          readOnly: true, // Phone number is not editable
                          keyboardType: TextInputType.phone,
                          suffixIcon: Icon(
                            Icons.phone,
                            size: MySize.size22,
                            color: AppColors.textGray,
                          ),
                        ),
                        Space.height(40),
                        Center(
                          child: Column(

                            children: [
                              SizedBox(
                                width: SizeConfig.screenWidth! * 0.5,
                                child: CustomButton(
                                  onTap: _saveProfile,
                                  text: "Save",
                                ),
                              ),
                              Space.height(10),
                              SizedBox(
                                width: SizeConfig.screenWidth! * 0.5,
                                child: _DeleteButton(
                                  onTap: () {
                                    _showDeleteAccountConfirmation();
                                  },
                                  text: "Delete Account",
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  void _showDeleteAccountConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).cardColor,
        title: Text("Delete Account",
            style: TextStyle(
                color: Theme.of(context).textTheme.bodyMedium?.color)),
        content: const Text(
            "Are you sure you want to delete your account? This action cannot be undone."),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              "Cancel",
              style: TextStyle(color: AppColors.primaryColor),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteAccount();
            },
            child: const Text("Delete",
                style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteAccount() async {
    // Show loading dialog
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: Theme.of(context).cardColor,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(color: AppColors.primaryColor),
              Space.height(16),
              Text(
                "Deleting your account...",
                style: TextStyle(
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ],
          ),
        ),
      );
    }

    try {
      // Delete the account (this also logs out the user at the end)
      bool success = await authService.deleteAccount();

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success && mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Account deleted successfully"),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate to login screen
        Navigator.pushNamedAndRemoveUntil(
            context, loginScreen, (route) => false);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Failed to delete account. Please try again."),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if it's open
      if (mounted) {
        Navigator.of(context).pop();

        String errorMessage = e.toString();
        if (errorMessage.contains("Exception: ")) {
          errorMessage = errorMessage.replaceAll("Exception: ", "");
        }

        // Show specific error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 5), // Longer duration for important messages
          ),
        );

        // If the error is about recent login, show additional guidance
        if (errorMessage.contains("log out and log back in")) {
          Future.delayed(Duration(seconds: 2), () {
            if (mounted) {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  backgroundColor: Theme.of(context).cardColor,
                  title: Text(
                    "Security Requirement",
                    style: TextStyle(
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: Text(
                    "For security reasons, you need to log out and log back in before deleting your account. This ensures that only you can delete your account.",
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        "OK",
                        style: TextStyle(color: AppColors.primaryColor),
                      ),
                    ),
                  ],
                ),
              );
            }
          });
        }
      }
    }
  }
}

class _DeleteButton extends StatelessWidget {
  final String text;
  final VoidCallback onTap;

  const _DeleteButton({required this.onTap, required this.text});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: MySize.size15),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(MySize.size30),
            color: Colors.red,
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withAlpha(90),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, 4))
            ]),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: Colors.white,
              fontSize: MySize.size15,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}