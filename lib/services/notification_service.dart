import 'dart:developer';
import 'dart:io';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:healo/services/firestore_service.dart';
import 'package:healo/services/navigation_service.dart';

/// Global function to handle background messages
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  log('Handling a background message: ${message.messageId}');
  log('Background message data: ${message.data}');

  if (message.notification != null) {
    log('Background message notification: ${message.notification!.title}');
  }
}

/// Service class to handle all push notification operations
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  final FirestoreService _firestoreService = FirestoreService();

  bool _isInitialized = false;

  /// Initialize the notification service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      log('Initializing notification service...');

      // Request notification permissions
      await _requestPermissions();

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Set up Firebase messaging handlers
      await _setupFirebaseMessaging();

      // Get and store FCM token
      await _handleTokenRefresh();

      _isInitialized = true;
      log('Notification service initialized successfully');
    } catch (e) {
      log('Error initializing notification service: $e');
    }
  }

  /// Request notification permissions
  Future<void> _requestPermissions() async {
    log('Requesting notification permissions...');

    NotificationSettings settings = await _firebaseMessaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    log('Notification permission status: ${settings.authorizationStatus}');

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      log('User granted notification permissions');
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      log('User granted provisional notification permissions');
    } else {
      log('User declined or has not accepted notification permissions');
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    log('Initializing local notifications...');

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/launcher_icon');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channel for Android
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }

    log('Local notifications initialized');
  }

  /// Create notification channel for Android
  Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'healo_notifications',
      'Healo Notifications',
      description: 'Notifications for medication reminders and health updates',
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    final androidPlugin = _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

    await androidPlugin?.createNotificationChannel(channel);
  }

  /// Set up Firebase messaging handlers
  Future<void> _setupFirebaseMessaging() async {
    log('Setting up Firebase messaging handlers...');

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // Handle notification tap when app is terminated
    RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      log('App opened from terminated state via notification');
      _handleNotificationTap(initialMessage);
    }

    // Listen for token refresh
    _firebaseMessaging.onTokenRefresh.listen(_onTokenRefresh);
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    log('Received foreground message: ${message.messageId}');
    log('Foreground message data: ${message.data}');

    if (message.notification != null) {
      log('Foreground message notification: ${message.notification!.title}');
      // Show notification in foreground
      await _showLocalNotification(message);
    }
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'healo_notifications',
      'Healo Notifications',
      channelDescription: 'Notifications for medication reminders and health updates',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    // Create JSON payload with medication details
    final medicationName = message.data['medication_name'] ?? '';
    final time = message.data['time'] ?? '';
    final dosage = message.data['dosage'] ?? '';

    final payload = '{"type":"medication_reminder","medication_name":"$medicationName","time":"$time","dosage":"$dosage"}';

    await _localNotifications.show(
      message.hashCode,
      message.notification?.title ?? 'Healo',
      message.notification?.body ?? 'You have a new notification',
      platformChannelSpecifics,
      payload: payload,
    );
  }

  /// Handle notification tap
  void _handleNotificationTap(RemoteMessage message) {
    log('Notification tapped: ${message.messageId}');
    log('Notification data: ${message.data}');

    // Navigate based on notification data
    _navigateBasedOnNotification(message.data);
  }

  /// Handle local notification tap
  void _onNotificationTapped(NotificationResponse response) {
    log('Local notification tapped: ${response.payload}');

    // Parse payload and navigate with medication details
    if (response.payload != null && response.payload!.contains('medication_reminder')) {
      _navigateToMedicationScreenWithPayload(response.payload!);
    } else {
      _navigateToMedicationScreen();
    }
  }

  /// Navigate based on notification data
  void _navigateBasedOnNotification(Map<String, dynamic> data) {
    String? type = data['type'];
    log('Navigating based on notification type: $type');

    switch (type) {
      case 'medication_reminder':
        // Extract medication details and navigate
        final medicationName = data['medication_name'] ?? '';
        final time = data['time'] ?? '';
        final dosage = data['dosage'] ?? '';
        _navigateToMedicationScreenWithDetails(medicationName, time, dosage);
        break;
      default:
        _navigateToMedicationScreen();
    }
  }

  /// Navigate to medication screen with payload
  void _navigateToMedicationScreenWithPayload(String payload) {
    log('Navigating to medication screen with payload: $payload');

    // Extract medication details from JSON payload
    final medicationName = _extractMedicationNameFromPayload(payload);
    final time = _extractTimeFromPayload(payload);
    final dosage = _extractDosageFromPayload(payload);

    _navigateToMedicationScreenWithDetails(medicationName, time, dosage);
  }

  /// Navigate to medication screen with medication details
  void _navigateToMedicationScreenWithDetails(String medicationName, String time, String dosage) {
    log('Navigating to medication screen with details: $medicationName, $time, $dosage');
    Future.delayed(const Duration(milliseconds: 500), () {
      NavigationService.navigateToMedicationScreenWithDetails(
        medicationName: medicationName,
        time: time,
        dosage: dosage,
      );
    });
  }

  /// Navigate to medication screen
  void _navigateToMedicationScreen() {
    log('Navigating to medication screen');
    Future.delayed(const Duration(milliseconds: 500), () {
      NavigationService.navigateToMedicationScreen();
    });
  }

  /// Extract medication name from JSON payload
  String _extractMedicationNameFromPayload(String payload) {
    try {
      final regex = RegExp(r'"medication_name":"([^"]+)"');
      final match = regex.firstMatch(payload);
      return match?.group(1)?.trim() ?? '';
    } catch (e) {
      log('Error extracting medication name: $e');
      return '';
    }
  }

  /// Extract time from JSON payload
  String _extractTimeFromPayload(String payload) {
    try {
      final regex = RegExp(r'"time":"([^"]+)"');
      final match = regex.firstMatch(payload);
      return match?.group(1)?.trim() ?? '';
    } catch (e) {
      log('Error extracting time: $e');
      return '';
    }
  }

  /// Extract dosage from JSON payload
  String _extractDosageFromPayload(String payload) {
    try {
      final regex = RegExp(r'"dosage":"([^"]+)"');
      final match = regex.firstMatch(payload);
      return match?.group(1)?.trim() ?? '';
    } catch (e) {
      log('Error extracting dosage: $e');
      return '';
    }
  }

  /// Get and store FCM token
  Future<void> _handleTokenRefresh() async {
    try {
      String? token = await _firebaseMessaging.getToken();
      if (token != null) {
        log('FCM Token: $token');
        await _storeFCMToken(token);

        // If user is authenticated but token wasn't stored, store it now
        if (_firestoreService.currentUserId != null) {
          log('User is authenticated, ensuring token is stored in Firestore');
          await _storeFCMToken(token);
        }
      } else {
        log('Failed to get FCM token');
      }
    } catch (e) {
      log('Error getting FCM token: $e');
    }
  }

  /// Handle token refresh
  Future<void> _onTokenRefresh(String token) async {
    log('FCM Token refreshed: $token');
    await _storeFCMToken(token);
  }

  /// Store FCM token in Firestore
  Future<void> _storeFCMToken(String token) async {
    try {
      // Check if user is authenticated before storing token
      if (_firestoreService.currentUserId != null) {
        await _firestoreService.updateFCMToken(token);
        log('FCM token stored successfully for user: ${_firestoreService.currentUserId}');
      } else {
        log('User not authenticated, FCM token not stored. Will retry when user logs in.');
      }
    } catch (e) {
      log('Error storing FCM token: $e');
    }
  }

  /// Get current FCM token
  Future<String?> getToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      log('Error getting current FCM token: $e');
      return null;
    }
  }

  /// Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      log('Subscribed to topic: $topic');
    } catch (e) {
      log('Error subscribing to topic $topic: $e');
    }
  }

  /// Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      log('Unsubscribed from topic: $topic');
    } catch (e) {
      log('Error unsubscribing from topic $topic: $e');
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    await _localNotifications.cancelAll();
    log('All notifications cleared');
  }

  /// Clear FCM token on logout
  Future<void> clearFCMToken() async {
    try {
      await _firestoreService.clearFCMToken();
      log('FCM token cleared on logout');
    } catch (e) {
      log('Error clearing FCM token on logout: $e');
    }
  }

  /// Refresh and store FCM token for authenticated user
  /// This should be called after successful login
  Future<void> refreshAndStoreToken() async {
    try {
      log('Refreshing and storing FCM token for authenticated user...');

      // Add a small delay to ensure Firebase Auth state is fully updated
      await Future.delayed(const Duration(milliseconds: 500));

      // Get fresh token with retry mechanism
      String? token;
      int retryCount = 0;
      const maxRetries = 3;

      while (token == null && retryCount < maxRetries) {
        try {
          token = await _firebaseMessaging.getToken();
          if (token != null) {
            log('Fresh FCM Token obtained: $token');
            await _storeFCMToken(token);
            break;
          }
        } catch (e) {
          log('Attempt ${retryCount + 1} failed to get FCM token: $e');
        }

        retryCount++;
        if (retryCount < maxRetries) {
          await Future.delayed(Duration(milliseconds: 1000 * retryCount));
        }
      }

      if (token == null) {
        log('Failed to get fresh FCM token after $maxRetries attempts');
      }
    } catch (e) {
      log('Error refreshing and storing FCM token: $e');
    }
  }

  /// Show medication reminder notification
  Future<void> showMedicationReminder({
    required String medicationName,
    required String time,
    String? dosage,
  }) async {
    final notificationId = DateTime.now().millisecondsSinceEpoch ~/ 1000;

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'healo_notifications',
      'Healo Notifications',
      channelDescription: 'Notifications for medication reminders',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      enableVibration: true,
      playSound: true,
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _localNotifications.show(
      notificationId,
      'Medication Reminder',
      'Time to take $medicationName${dosage != null ? ' ($dosage)' : ''}',
      platformChannelSpecifics,
      payload: 'medication_reminder',
    );

    log('Medication reminder shown: $medicationName at $time');
  }
}
