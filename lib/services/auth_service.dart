import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:healo/services/notification_service.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  int? _resendToken;

  /// Verifies the phone number and sends OTP
  Future<void> verifyPhoneNumber(
      String phoneNumber, Function(String, int?) codeSent) async {
    await _auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      timeout: const Duration(seconds: 60),
      forceResendingToken: _resendToken,

      /// Auto-verification when OTP is detected automatically (e.g., Android)
      verificationCompleted: (PhoneAuthCredential credential) async {
        try {
          await _auth.signInWithCredential(credential);
          log("Auto verification completed");
        } catch (e) {
          log("Auto verification error: $e");
        }
      },

      /// If verification fails (e.g., invalid number)
      verificationFailed: (FirebaseAuthException e) {
        log("Verification failed: ${e.message}");
      },

      /// When the OTP is sent, the verificationId is required for signing in
      codeSent: (String verificationId, int? resendToken) {
        _resendToken = resendToken;
        codeSent(verificationId, resendToken);
        log("OTP sent to $phoneNumber");
      },

      /// If the OTP is not auto-retrieved within the timeout
      codeAutoRetrievalTimeout: (String verificationId) {
        log("Auto retrieval timeout");
      },
    );
  }

  /// Signs in the user with the OTP
  Future<UserCredential?> signInWithOTP(
      String verificationId, String smsCode) async {
    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: verificationId, smsCode: smsCode);

      UserCredential userCredential =
          await _auth.signInWithCredential(credential);
      log("User signed in: ${userCredential.user?.uid}");

      // Initialize notifications after successful login
      try {
        await NotificationService().initialize();
        log("Notifications initialized after login");

        // Refresh and store FCM token for the authenticated user
        await NotificationService().refreshAndStoreToken();
        log("FCM token refreshed and stored after login");
      } catch (e) {
        log("Error initializing notifications after login: $e");
      }

      // Check if user has a pending deletion request
      await _checkPendingDeletionRequest(userCredential.user);

      return userCredential;
    } catch (e) {
      log("Sign-in error: $e");
      return null;
    }
  }

  Future<void> logout() async {
    try {
      // Clear FCM token before signing out for security reasons
      // This prevents notifications from being sent to devices after logout
      // and ensures user privacy when switching accounts
      try {
        await NotificationService().clearFCMToken();
        log("FCM token cleared during logout for security");
      } catch (e) {
        log("Error clearing FCM token during logout: $e");
        // Continue with logout even if this fails
      }

      // Sign out first
      await _auth.signOut();

      // Instead of clearing persistence (which causes the error),
      // we'll terminate and re-initialize Firestore
      try {
        await FirebaseFirestore.instance.terminate();
        await FirebaseFirestore.instance.clearPersistence();
        await FirebaseFirestore.instance.enableNetwork();
      } catch (e) {
        log("Firestore reset error (non-critical): $e");
        // Continue with logout even if this fails
      }

      log("User logged out successfully");
    } catch (e) {
      log("Logout error: $e");
      rethrow; // Rethrow to handle in UI
    }
  }

  Future<bool> deleteAccount() async {
    try {
      // Get the current user
      User? user = _auth.currentUser;
      if (user == null) {
        throw Exception("No user is currently signed in");
      }

      String? phoneNumber = user.phoneNumber;
      if (phoneNumber == null) {
        throw Exception("User phone number not available");
      }

      log("Starting account deletion process for user: $phoneNumber");

      // Try the standard deletion flow first
      try {
        // Step 1: Try re-authentication
        await _reauthenticateUser(user);
        log("User re-authenticated successfully");

        // Step 2: Delete the Firebase Auth account FIRST
        await user.delete();
        log("Firebase Auth account deleted successfully");

        // Step 3: Delete user data from Firestore AFTER successful auth deletion
        await _deleteUserData(phoneNumber);
        log("User data deleted from Firestore successfully");

        log("User account deleted completely");
        return true;
      } catch (authError) {
        log("Standard deletion failed: $authError");

        // If it's a requires-recent-login error, try alternative approach
        if (authError.toString().contains('requires-recent-login')) {
          log("Attempting alternative deletion approach due to recent login requirement");
          return await _deleteAccountAlternative(user, phoneNumber);
        }

        // If it's our custom re-authentication message, throw it to the UI
        if (authError.toString().contains("Please log out and log back in")) {
          rethrow;
        }

        // For other auth errors, rethrow
        rethrow;
      }
    } catch (e) {
      log("Delete account error: $e");

      // If the error is about recent login, provide a more helpful message
      if (e.toString().contains('requires-recent-login')) {
        throw Exception("For security reasons, please log out and log back in before deleting your account.");
      }

      return false;
    }
  }

  /// Alternative deletion approach when standard method fails due to recent login requirement
  Future<bool> _deleteAccountAlternative(User user, String phoneNumber) async {
    try {
      log("Using alternative deletion approach for user: $phoneNumber");

      // Mark the account for deletion in Firestore first
      await FirebaseFirestore.instance
          .collection("users")
          .doc(phoneNumber)
          .update({
        'accountDeletionRequested': true,
        'accountDeletionRequestedAt': FieldValue.serverTimestamp(),
      });

      log("Account marked for deletion in Firestore");

      // Sign out the user
      await _auth.signOut();
      log("User signed out successfully");

      // Inform the user they need to log back in to complete deletion
      throw Exception("To complete account deletion, please log back in and try again. This is a security requirement.");

    } catch (e) {
      log("Alternative deletion approach failed: $e");
      rethrow;
    }
  }

  /// Re-authenticate the user to satisfy Firebase's recent login requirement
  Future<void> _reauthenticateUser(User user) async {
    try {
      // For phone auth users, token refresh might not be sufficient for sensitive operations
      // We'll try multiple approaches to satisfy the recent login requirement

      // Approach 1: Reload user and refresh token
      await user.reload();
      await user.getIdToken(true); // Force refresh the token

      // Approach 2: Check if the user's metadata indicates recent sign-in
      final metadata = user.metadata;
      final lastSignIn = metadata.lastSignInTime;
      final now = DateTime.now();

      if (lastSignIn != null) {
        final timeSinceLastSignIn = now.difference(lastSignIn);
        log("Time since last sign-in: ${timeSinceLastSignIn.inMinutes} minutes");

        // If the user signed in more than 5 minutes ago, they likely need to re-authenticate
        if (timeSinceLastSignIn.inMinutes > 5) {
          log("User signed in more than 5 minutes ago, may need fresh authentication");
          throw Exception("Please log out and log back in before deleting your account.");
        }
      }

      log("User re-authentication check completed successfully");
    } catch (e) {
      log("Re-authentication failed: $e");

      // If it's already our custom exception, re-throw it
      if (e.toString().contains("Please log out and log back in")) {
        rethrow;
      }

      // For other errors, provide the re-authentication message
      throw Exception("Please log out and log back in before deleting your account.");
    }
  }

  Future<void> _deleteUserData(String phoneNumber) async {
    try {
      log("Starting deletion of user data for: $phoneNumber");

      // List of all collections that contain user data
      final collections = [
        'users',
        'daily_medication',
        'weekly_medication',
        'monthly_medication',
        'blood_pressure',
        'health_data',
        'diabetes',
        'hba1c',
        'kidney',
        'liver',
        'period_data',
        'thyroid',
        'water_intake',
      ];

      // Delete from each collection
      for (String collection in collections) {
        try {
          await FirebaseFirestore.instance
              .collection(collection)
              .doc(phoneNumber)
              .delete();
          log("Deleted data from $collection collection");
        } catch (e) {
          log("Error deleting from $collection: $e");
          // Continue with other collections even if one fails
        }
      }

      // Also delete any uploaded files from Firebase Storage
      try {
        await _deleteUserStorageFiles(phoneNumber);
      } catch (e) {
        log("Error deleting storage files: $e");
        // Continue even if storage deletion fails
      }

      log("User data deletion completed for: $phoneNumber");
    } catch (e) {
      log("Error deleting user data: $e");
      rethrow; // Re-throw to handle in calling method
    }
  }

  /// Delete user files from Firebase Storage
  Future<void> _deleteUserStorageFiles(String phoneNumber) async {
    try {
      // Delete profile images
      try {
        await FirebaseStorage.instance
            .ref()
            .child('profiles/$phoneNumber')
            .listAll()
            .then((result) async {
          for (var item in result.items) {
            await item.delete();
            log("Deleted profile file: ${item.name}");
          }
        });
      } catch (e) {
        log("Error deleting profile files: $e");
      }

      // Delete PDF reports
      try {
        await FirebaseStorage.instance
            .ref()
            .child('pdfs/$phoneNumber')
            .listAll()
            .then((result) async {
          for (var item in result.items) {
            await item.delete();
            log("Deleted PDF file: ${item.name}");
          }
        });
      } catch (e) {
        log("Error deleting PDF files: $e");
      }

      log("Storage files deletion completed for: $phoneNumber");
    } catch (e) {
      log("Error deleting storage files: $e");
      // Don't rethrow - storage deletion is not critical
    }
  }

  /// Check if user has a pending deletion request and complete it
  Future<void> _checkPendingDeletionRequest(User? user) async {
    if (user?.phoneNumber == null) return;

    try {
      final doc = await FirebaseFirestore.instance
          .collection("users")
          .doc(user!.phoneNumber)
          .get();

      if (doc.exists) {
        final data = doc.data();
        final hasPendingDeletion = data?['accountDeletionRequested'] == true;

        if (hasPendingDeletion) {
          log("Found pending deletion request for user: ${user.phoneNumber}");

          // Complete the deletion process now that user is freshly authenticated
          try {
            await user.delete();
            log("Firebase Auth account deleted successfully (pending request)");

            await _deleteUserData(user.phoneNumber!);
            log("User data deleted successfully (pending request)");

            log("Pending account deletion completed successfully");
          } catch (e) {
            log("Error completing pending deletion: $e");
            // Remove the pending deletion flag if it fails
            await FirebaseFirestore.instance
                .collection("users")
                .doc(user.phoneNumber)
                .update({
              'accountDeletionRequested': FieldValue.delete(),
              'accountDeletionRequestedAt': FieldValue.delete(),
            });
          }
        }
      }
    } catch (e) {
      log("Error checking pending deletion request: $e");
    }
  }
}
